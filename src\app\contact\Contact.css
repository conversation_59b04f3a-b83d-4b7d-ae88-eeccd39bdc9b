/* Main contact page layout */
.app__contactpage {
  min-height: 100vh;
  background: var(--color-black);
  color: #fff;
  padding: 0;
}

/* Enhanced Header Section */
.contact-header {
  padding: 5rem 2rem 3rem;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.contact-header-content {
  position: relative;
  z-index: 2;
}

.contact-main-title {
  font-family: var(--font-base);
  font-size: 4.5rem;
  font-weight: 400;
  color: #fff;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  letter-spacing: -2px;
}

.contact-main-description {
  font-family: var(--font-alt);
  font-size: 1.2rem;
  line-height: 1.8;
  color: #e0e0e0;
  max-width: 700px;
  margin: 0 auto 2rem;
}

.contact-header-divider {
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-golden), transparent);
  margin: 0 auto;
  position: relative;
}

.contact-header-divider::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--color-golden);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(220, 202, 135, 0.5);
}

/* Enhanced Main Content Layout */
.contact-main-content {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 4rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem;
  align-items: start;
}

/* Enhanced Contact Form Section */
.contact-form-section {
  max-width: 400px;
  background: rgba(15, 40, 70, 0.3);
  border-radius: 12px;
  padding: 2.5rem;
  border: 1px solid rgba(220, 202, 135, 0.1);
  backdrop-filter: blur(10px);
}

.contact-form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.contact-form-title {
  font-family: var(--font-base);
  font-size: 2rem;
  color: #fff;
  margin-bottom: 0.5rem;
  font-weight: 400;
}

.contact-form-subtitle {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: var(--color-golden);
  text-transform: uppercase;
  letter-spacing: 2px;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-row {
  width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

.form-group label {
  font-family: var(--font-alt);
  font-size: 0.85rem;
  color: var(--color-golden);
  margin-bottom: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.input-wrapper {
  position: relative;
}

.form-group input,
.form-group textarea {
  background: transparent;
  border: none;
  color: #fff;
  padding: 1rem 0 0.8rem;
  font-family: var(--font-alt);
  font-size: 1rem;
  width: 100%;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #888;
  font-size: 0.9rem;
  transition: opacity 0.3s ease;
}

.form-group input:focus::placeholder,
.form-group textarea:focus::placeholder {
  opacity: 0.5;
}

.input-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #555;
  transition: all 0.3s ease;
}

.input-underline::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-golden);
  transition: width 0.3s ease;
}

.form-group input:focus + .input-underline::after,
.form-group textarea:focus + .input-underline::after {
  width: 100%;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
  padding-top: 1rem;
}

.form-submit {
  margin-top: 1rem;
  text-align: center;
}

.submit-btn {
  background: var(--color-golden);
  color: var(--color-black);
  border: 2px solid var(--color-golden);
  padding: 1rem 2.5rem;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  position: relative;
  overflow: hidden;
}

.submit-btn:hover {
  background: transparent;
  color: var(--color-golden);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 202, 135, 0.3);
}

.btn-arrow {
  transition: transform 0.3s ease;
  font-size: 1.1rem;
}

.submit-btn:hover .btn-arrow {
  transform: translateX(4px);
}

.form-success {
  background: rgba(15, 40, 70, 0.8);
  border: 2px solid var(--color-golden);
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.success-icon {
  width: 60px;
  height: 60px;
  background: var(--color-golden);
  color: var(--color-black);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0 auto 1.5rem;
  box-shadow: 0 0 20px rgba(220, 202, 135, 0.4);
}

.form-success h3 {
  font-family: var(--font-base);
  color: #fff;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 400;
}

.form-success p {
  font-family: var(--font-alt);
  color: #e0e0e0;
  font-size: 1rem;
  line-height: 1.6;
}

/* Working Hours Section */
.working-hours-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.working-hours-card {
  background: var(--color-golden);
  padding: 2.5rem 2rem;
  border-radius: 4px;
  text-align: center;
  min-width: 280px;
}

.working-hours-card h2 {
  font-family: var(--font-base);
  font-size: 1.8rem;
  color: var(--color-black);
  margin-bottom: 2rem;
  font-weight: 400;
}

.hours-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
}

.hours-item:last-child {
  margin-bottom: 0;
}

.day {
  font-family: var(--font-alt);
  font-size: 1rem;
  color: var(--color-black);
  font-weight: 500;
  margin-bottom: 0.3rem;
}

.time {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: var(--color-black);
  opacity: 0.8;
}

/* Contact Information Section */
.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 350px;
}

.contact-info-item {
  display: flex;
  flex-direction: column;
}

.contact-info-item h3 {
  font-family: var(--font-alt);
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0.8rem;
  font-weight: 500;
}

.contact-info-item p {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 0.3rem;
  line-height: 1.4;
}

.contact-info-item p:last-child {
  margin-bottom: 0;
}

/* Compact Map Column for Three-Column Layout */
.contact-map-column {
  display: flex;
  flex-direction: column;
  max-width: 350px;
}

.map-frame-compact {
  position: relative;
  height: 400px;
  width: 100%;
}

.map-golden-border-compact {
  position: relative;
  height: 100%;
  border: 2px solid var(--color-golden);
  border-radius: 6px;
  padding: 0.5rem;
  background: linear-gradient(45deg,
    rgba(220, 202, 135, 0.1) 0%,
    rgba(220, 202, 135, 0.05) 50%,
    rgba(220, 202, 135, 0.1) 100%);
  box-shadow:
    0 0 15px rgba(220, 202, 135, 0.3),
    inset 0 0 15px rgba(220, 202, 135, 0.1);
}

.map-inner-frame-compact {
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(220, 202, 135, 0.3);
}

.map-inner-frame-compact iframe {
  filter: sepia(10%) saturate(90%) hue-rotate(15deg);
  transition: filter 0.3s ease;
}

.map-frame-compact:hover .map-inner-frame-compact iframe {
  filter: sepia(0%) saturate(100%) hue-rotate(0deg);
}

/* Compact Decorative Corner Elements */
.map-corner-compact {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-golden);
  z-index: 10;
}

.map-corner-compact--top-left {
  top: -2px;
  left: -2px;
  border-right: none;
  border-bottom: none;
  border-top-left-radius: 6px;
}

.map-corner-compact--top-right {
  top: -2px;
  right: -2px;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 6px;
}

.map-corner-compact--bottom-left {
  bottom: -2px;
  left: -2px;
  border-right: none;
  border-top: none;
  border-bottom-left-radius: 6px;
}

.map-corner-compact--bottom-right {
  bottom: -2px;
  right: -2px;
  border-left: none;
  border-top: none;
  border-bottom-right-radius: 6px;
}

.map-corner-compact::before {
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  background: var(--color-golden);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 6px rgba(220, 202, 135, 0.6);
}





/* Responsive Styles */
@media screen and (max-width: 1024px) {
  .contact-main-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .contact-form-section,
  .contact-map-column {
    max-width: 100%;
  }

  .working-hours-section {
    order: -1;
  }

  /* Compact map responsive styles */
  .contact-map-column {
    max-width: 500px;
    margin: 0 auto;
  }

  .map-frame-compact {
    height: 350px;
  }
}

@media screen and (max-width: 768px) {
  .contact-header {
    padding: 3rem 1rem 2rem;
    text-align: center;
  }

  .contact-main-title {
    font-size: 3rem;
  }

  .contact-main-content {
    padding: 1rem;
    gap: 2rem;
  }

  .working-hours-card {
    padding: 2rem 1.5rem;
    min-width: auto;
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
  }

  /* Compact map responsive styles for tablets */
  .contact-map-column {
    max-width: 400px;
  }

  .map-frame-compact {
    height: 300px;
  }

  .map-golden-border-compact {
    padding: 0.4rem;
    border-width: 2px;
  }

  .map-corner-compact {
    width: 14px;
    height: 14px;
  }
}

@media screen and (max-width: 576px) {
  .contact-main-title {
    font-size: 2.5rem;
  }

  .contact-main-description {
    font-size: 1rem;
  }

  .contact-main-content {
    gap: 1.5rem;
  }

  .working-hours-card {
    padding: 1.5rem 1rem;
  }

  .working-hours-card h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .contact-form-section {
    max-width: 100%;
  }

  /* Compact map responsive styles for mobile */
  .contact-map-column {
    max-width: 100%;
  }

  .map-frame-compact {
    height: 250px;
  }

  .map-golden-border-compact {
    padding: 0.3rem;
    border-width: 1px;
  }

  .map-corner-compact {
    width: 12px;
    height: 12px;
  }

  .map-corner-compact::before {
    width: 4px;
    height: 4px;
  }
}