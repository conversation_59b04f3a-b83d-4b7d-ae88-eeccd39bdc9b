"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { SubHeading, Navbar } from "@/components";
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaInstagram, FaFacebookF, FaTwitter, FaDirections } from "react-icons/fa";
import { images } from "@/constants";
import "./Contact.css";

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });

  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would normally send the form data to your backend
    console.log("Form submitted:", formData);
    setFormSubmitted(true);

    // Reset form after submission
    setTimeout(() => {
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      setFormSubmitted(false);
    }, 3000);
  };



  return (
    <>
      <Navbar />
      <div className="app__contactpage">
        {/* Enhanced Header Section */}
        <div className="contact-header">
          <div className="contact-header-content">
            <SubHeading title="Get In Touch" />
            <h1 className="contact-main-title">Contact Us</h1>
            <p className="contact-main-description">
              Welcome to our restaurant, where we create exceptional dining experiences with carefully crafted dishes
              and warm hospitality. We'd love to hear from you and assist with any inquiries or reservations.
            </p>
            <div className="contact-header-divider"></div>
          </div>
        </div>

        {/* Main Contact Content */}
        <div className="contact-main-content">
          {/* Enhanced Contact Form */}
          <div className="contact-form-section">
            <div className="contact-form-header">
              <h2 className="contact-form-title">Send us a Message</h2>
              <p className="contact-form-subtitle">We'd love to hear from you</p>
            </div>

            {formSubmitted ? (
              <div className="form-success">
                <div className="success-icon">✓</div>
                <h3>Message Sent Successfully!</h3>
                <p>Thank you for reaching out. We&apos;ll get back to you within 24 hours.</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="contact-form">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="name">First Name *</label>
                    <div className="input-wrapper">
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        placeholder="Enter your first name"
                      />
                      <div className="input-underline"></div>
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="email">Email Address *</label>
                    <div className="input-wrapper">
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        placeholder="Enter your email address"
                      />
                      <div className="input-underline"></div>
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="subject">Subject</label>
                    <div className="input-wrapper">
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        placeholder="What is this regarding?"
                      />
                      <div className="input-underline"></div>
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="message">Your Message *</label>
                    <div className="input-wrapper">
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleChange}
                        required
                        placeholder="Tell us how we can help you..."
                        rows="4"
                      ></textarea>
                      <div className="input-underline"></div>
                    </div>
                  </div>
                </div>

                <div className="form-submit">
                  <button type="submit" className="submit-btn">
                    <span>Send Message</span>
                    <div className="btn-arrow">→</div>
                  </button>
                </div>
              </form>
            )}
          </div>

          {/* Enhanced Working Hours */}
          <div className="working-hours-section">
            <div className="working-hours-card">
              <div className="hours-header">
                <div className="hours-icon">
                  <FaClock />
                </div>
                <h2>Working Hours</h2>
                <p className="hours-subtitle">We're here to serve you</p>
              </div>

              <div className="hours-content">
                <div className="hours-item">
                  <div className="hours-day-group">
                    <span className="day">Monday - Friday</span>
                    <span className="day-type">Weekdays</span>
                  </div>
                  <div className="hours-time-group">
                    <span className="time">8:00 AM - 12:00 AM</span>
                  </div>
                </div>

                <div className="hours-divider"></div>

                <div className="hours-item">
                  <div className="hours-day-group">
                    <span className="day">Saturday - Sunday</span>
                    <span className="day-type">Weekends</span>
                  </div>
                  <div className="hours-time-group">
                    <span className="time">7:00 AM - 11:00 PM</span>
                  </div>
                </div>
              </div>

              <div className="hours-footer">
                <p className="hours-note">
                  <FaPhone className="note-icon" />
                  Call ahead for large parties
                </p>
              </div>
            </div>
          </div>

          {/* Enhanced Google Maps Integration */}
          <div className="contact-map-column">
            <div className="map-header-compact">
              <div className="map-icon">
                <FaMapMarkerAlt />
              </div>
              <h2 className="map-title-compact">Find Us</h2>
              <p className="map-subtitle-compact">Visit our restaurant</p>
            </div>

            <div className="map-frame-compact">
              <div className="map-golden-border-compact">
                <div className="map-inner-frame-compact">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.9663095343008!2d-74.00425878459418!3d40.74844097932681!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c259bf5c1654f3%3A0xc80f9cfce5383d5d!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sus!4v1635959542208!5m2!1sen!2sus"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen=""
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Restaurant Location"
                  ></iframe>
                </div>
              </div>

              {/* Enhanced decorative corners */}
              <div className="map-corner-compact map-corner-compact--top-left"></div>
              <div className="map-corner-compact map-corner-compact--top-right"></div>
              <div className="map-corner-compact map-corner-compact--bottom-left"></div>
              <div className="map-corner-compact map-corner-compact--bottom-right"></div>
            </div>

            <div className="map-info-compact">
              <div className="map-address">
                <h3>123 Gourmet Avenue</h3>
                <p>Culinary District, NY 10001</p>
              </div>

              <div className="map-actions-compact">
                <a
                  href="https://maps.google.com/?q=123+Gourmet+Avenue,+Culinary+District,+NY+10001"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="map-action-btn-compact"
                >
                  <FaDirections />
                  <span>Get Directions</span>
                </a>
              </div>
            </div>
          </div>
        </div>


      </div>
    </>
  );
};

export default ContactPage; 